/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution

import java.io.File

import org.apache.spark.sql.{QueryTest, Row}
import org.apache.spark.sql.execution.datasources.WriteFilesExec
import org.apache.spark.sql.execution.exchange.ShuffleExchangeExec
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.internal.SQLConf
import org.apache.spark.sql.test.SharedSparkSession

class SmallFileMergingIntegrationSuite extends QueryTest with SharedSparkSession {

  test("end-to-end small file merging with INSERT INTO") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key -> "50",
      SQLConf.SHUFFLE_PARTITIONS.key -> "100") {
      
      withTempDir { dir =>
        val tablePath = dir.getCanonicalPath + "/test_table"
        
        // Create a table
        sql(s"""
          CREATE TABLE test_table (
            id BIGINT,
            value STRING
          ) USING PARQUET
          LOCATION '$tablePath'
        """)
        
        // Insert data that would create many small files without merging
        sql("""
          INSERT INTO test_table
          SELECT id, CONCAT('value_', id) as value
          FROM range(0, 10000, 1, 100)
        """)
        
        // Verify data was written correctly
        val result = sql("SELECT COUNT(*) FROM test_table").collect()
        assert(result(0).getLong(0) === 10000L)
        
        // Check that files were created
        val files = new File(tablePath).listFiles()
          .filter(_.getName.endsWith(".parquet"))
        assert(files.length > 0)
        
        // Clean up
        sql("DROP TABLE test_table")
      }
    }
  }

  test("small file merging with partitioned tables") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true") {
      
      withTempDir { dir =>
        val tablePath = dir.getCanonicalPath + "/partitioned_table"
        
        // Create a partitioned table
        sql(s"""
          CREATE TABLE partitioned_table (
            id BIGINT,
            value STRING,
            partition_col STRING
          ) USING PARQUET
          PARTITIONED BY (partition_col)
          LOCATION '$tablePath'
        """)
        
        // Insert data into multiple partitions
        sql("""
          INSERT INTO partitioned_table
          SELECT 
            id, 
            CONCAT('value_', id) as value,
            CASE 
              WHEN id % 3 = 0 THEN 'A'
              WHEN id % 3 = 1 THEN 'B'
              ELSE 'C'
            END as partition_col
          FROM range(0, 3000, 1, 50)
        """)
        
        // Verify data was written correctly
        val result = sql("SELECT COUNT(*) FROM partitioned_table").collect()
        assert(result(0).getLong(0) === 3000L)
        
        // Verify partitions were created
        val partitionA = sql("SELECT COUNT(*) FROM partitioned_table WHERE partition_col = 'A'").collect()
        val partitionB = sql("SELECT COUNT(*) FROM partitioned_table WHERE partition_col = 'B'").collect()
        val partitionC = sql("SELECT COUNT(*) FROM partitioned_table WHERE partition_col = 'C'").collect()
        
        assert(partitionA(0).getLong(0) === 1000L)
        assert(partitionB(0).getLong(0) === 1000L)
        assert(partitionC(0).getLong(0) === 1000L)
        
        // Clean up
        sql("DROP TABLE partitioned_table")
      }
    }
  }

  test("small file merging should not interfere with bucketed tables") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true") {
      
      withTempDir { dir =>
        val tablePath = dir.getCanonicalPath + "/bucketed_table"
        
        // Create a bucketed table
        sql(s"""
          CREATE TABLE bucketed_table (
            id BIGINT,
            value STRING
          ) USING PARQUET
          CLUSTERED BY (id) INTO 8 BUCKETS
          LOCATION '$tablePath'
        """)
        
        // Insert data
        sql("""
          INSERT INTO bucketed_table
          SELECT id, CONCAT('value_', id) as value
          FROM range(0, 1000, 1, 20)
        """)
        
        // Verify data was written correctly
        val result = sql("SELECT COUNT(*) FROM bucketed_table").collect()
        assert(result(0).getLong(0) === 1000L)
        
        // Clean up
        sql("DROP TABLE bucketed_table")
      }
    }
  }

  test("small file merging configuration changes take effect") {
    // Test with small target size
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key -> "10") {
      
      withTempDir { dir =>
        val path1 = dir.getCanonicalPath + "/small_target"
        
        val df = spark.range(0, 1000, 1, 50).toDF("id")
        df.write.mode("overwrite").parquet(path1)
        
        val readDf1 = spark.read.parquet(path1)
        assert(readDf1.count() === 1000)
      }
    }
    
    // Test with large target size
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key -> "500") {
      
      withTempDir { dir =>
        val path2 = dir.getCanonicalPath + "/large_target"
        
        val df = spark.range(0, 1000, 1, 50).toDF("id")
        df.write.mode("overwrite").parquet(path2)
        
        val readDf2 = spark.read.parquet(path2)
        assert(readDf2.count() === 1000)
      }
    }
  }

  test("small file merging works with complex queries") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true") {
      
      withTempDir { dir =>
        val tablePath = dir.getCanonicalPath + "/complex_query_table"
        
        // Create source tables
        val df1 = spark.range(0, 1000, 1, 30).toDF("id").withColumn("type", lit("A"))
        val df2 = spark.range(1000, 2000, 1, 30).toDF("id").withColumn("type", lit("B"))
        
        df1.createOrReplaceTempView("source1")
        df2.createOrReplaceTempView("source2")
        
        // Create target table
        sql(s"""
          CREATE TABLE complex_target (
            id BIGINT,
            type STRING,
            computed_value BIGINT
          ) USING PARQUET
          LOCATION '$tablePath'
        """)
        
        // Insert with complex query involving joins and aggregations
        sql("""
          INSERT INTO complex_target
          SELECT 
            s1.id,
            s1.type,
            s1.id * 2 as computed_value
          FROM source1 s1
          UNION ALL
          SELECT 
            s2.id,
            s2.type,
            s2.id * 3 as computed_value
          FROM source2 s2
        """)
        
        // Verify results
        val result = sql("SELECT COUNT(*) FROM complex_target").collect()
        assert(result(0).getLong(0) === 2000L)
        
        val typeACount = sql("SELECT COUNT(*) FROM complex_target WHERE type = 'A'").collect()
        val typeBCount = sql("SELECT COUNT(*) FROM complex_target WHERE type = 'B'").collect()
        
        assert(typeACount(0).getLong(0) === 1000L)
        assert(typeBCount(0).getLong(0) === 1000L)
        
        // Clean up
        sql("DROP TABLE complex_target")
      }
    }
  }
}
