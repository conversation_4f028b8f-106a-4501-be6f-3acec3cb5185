/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.exchange

import java.io.File

import org.apache.spark.sql.{QueryTest, Row}
import org.apache.spark.sql.catalyst.plans.physical.RoundRobinPartitioning
import org.apache.spark.sql.execution.datasources.WriteFilesExec
import org.apache.spark.sql.internal.SQLConf
import org.apache.spark.sql.test.SharedSparkSession

class SmallFileMergingSuite extends QueryTest with SharedSparkSession {

  test("small file merging should be enabled by default") {
    assert(spark.conf.get(SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key).toBoolean === true)
    assert(spark.conf.get(SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key).toLong === 250L)
  }

  test("small file merging should insert repartition before WriteFilesExec") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key -> "100",
      SQLConf.SHUFFLE_PARTITIONS.key -> "200") {
      
      withTempDir { dir =>
        val path = dir.getCanonicalPath
        
        // Create a DataFrame with enough data to trigger repartitioning
        val df = spark.range(0, 10000, 1, 200).toDF("id")
        
        // Write to parquet to trigger WriteFilesExec
        df.write.mode("overwrite").parquet(path)
        
        // Verify the write operation completed successfully
        val readDf = spark.read.parquet(path)
        assert(readDf.count() === 10000)
      }
    }
  }

  test("small file merging should be disabled when configuration is false") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "false") {
      
      withTempDir { dir =>
        val path = dir.getCanonicalPath
        
        // Create a DataFrame
        val df = spark.range(0, 1000, 1, 50).toDF("id")
        
        // Write to parquet
        df.write.mode("overwrite").parquet(path)
        
        // Verify the write operation completed successfully
        val readDf = spark.read.parquet(path)
        assert(readDf.count() === 1000)
      }
    }
  }

  test("small file merging should not apply when adaptive execution is disabled") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "false",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true") {
      
      withTempDir { dir =>
        val path = dir.getCanonicalPath
        
        // Create a DataFrame
        val df = spark.range(0, 1000, 1, 50).toDF("id")
        
        // Write to parquet
        df.write.mode("overwrite").parquet(path)
        
        // Verify the write operation completed successfully
        val readDf = spark.read.parquet(path)
        assert(readDf.count() === 1000)
      }
    }
  }

  test("small file merging should respect target file size configuration") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_TARGET_SIZE_MB.key -> "50") {
      
      withTempDir { dir =>
        val path = dir.getCanonicalPath
        
        // Create a DataFrame with data
        val df = spark.range(0, 5000, 1, 100).toDF("id")
        
        // Write to parquet
        df.write.mode("overwrite").parquet(path)
        
        // Verify the write operation completed successfully
        val readDf = spark.read.parquet(path)
        assert(readDf.count() === 5000)
        
        // Check that files were created (basic verification)
        val files = new File(path).listFiles().filter(_.getName.endsWith(".parquet"))
        assert(files.length > 0)
      }
    }
  }

  test("EnsureRequirements should apply small file merging transformation") {
    withSQLConf(
      SQLConf.ADAPTIVE_EXECUTION_ENABLED.key -> "true",
      SQLConf.ADAPTIVE_SMALL_FILE_MERGING_ENABLED.key -> "true") {
      
      // Create a simple plan to test the rule
      val df = spark.range(0, 1000, 1, 100).toDF("id")
      val logicalPlan = df.queryExecution.analyzed
      val sparkPlan = df.queryExecution.sparkPlan
      
      // Apply EnsureRequirements rule
      val ensureRequirements = EnsureRequirements()
      val transformedPlan = ensureRequirements.apply(sparkPlan)
      
      // The plan should be transformed successfully
      assert(transformedPlan != null)
    }
  }
}
