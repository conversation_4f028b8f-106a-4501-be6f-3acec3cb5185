# Small File Merging in Spark SQL

## Overview

The Small File Merging feature in Spark SQL automatically inserts repartition operations before output stages to prevent small file problems in data lakes and Hive tables. This feature proactively merges files during Spark job execution rather than requiring post-processing steps.

## How It Works

1. **Detection**: When a `WriteFilesExec` operation is encountered during query planning
2. **Analysis**: Examines the child plan's output size from shuffle statistics or logical plan statistics
3. **Calculation**: Computes optimal partition count based on: `dataSize / targetFileSize`
4. **Decision**: Only repartitions if current partitions significantly exceed optimal count (> 2x)
5. **Execution**: Inserts `ShuffleExchangeExec` with `RoundRobinPartitioning` before the write operation

## Configuration

### Enable/Disable Feature

```sql
-- Enable small file merging (enabled by default when AQE is enabled)
SET spark.sql.adaptive.smallFileMerging.enabled=true;

-- Disable small file merging
SET spark.sql.adaptive.smallFileMerging.enabled=false;
```

### Target File Size

```sql
-- Set target file size to 128MB per partition (default is 250MB)
SET spark.sql.adaptive.smallFileMerging.targetFileSizeMB=128;

-- Set target file size to 512MB per partition
SET spark.sql.adaptive.smallFileMerging.targetFileSizeMB=512;
```

### Prerequisites

The feature requires Adaptive Query Execution to be enabled:

```sql
-- Enable adaptive query execution (enabled by default in Spark 3.0+)
SET spark.sql.adaptive.enabled=true;
```

## Configuration Options

| Configuration Key | Default Value | Description |
|-------------------|---------------|-------------|
| `spark.sql.adaptive.smallFileMerging.enabled` | `true` | Enable/disable automatic small file merging |
| `spark.sql.adaptive.smallFileMerging.targetFileSizeMB` | `250` | Target file size in MB for optimal partitioning |
| `spark.sql.adaptive.enabled` | `true` | Must be enabled for small file merging to work |

## Use Cases

### Data Lake Writes

```sql
-- Writing to a data lake with optimal file sizes
INSERT INTO my_data_lake_table
SELECT * FROM large_source_table
WHERE date_partition = '2023-01-01';
```

### Hive Table Inserts

```sql
-- Inserting into partitioned Hive tables
INSERT INTO hive_table PARTITION (year=2023, month=1)
SELECT id, name, value FROM source_data;
```

### ETL Pipelines

```sql
-- ETL job with automatic file size optimization
CREATE TABLE optimized_output AS
SELECT 
  customer_id,
  SUM(amount) as total_amount,
  COUNT(*) as transaction_count
FROM transactions
GROUP BY customer_id;
```

## Benefits

1. **Automatic Optimization**: No manual intervention required
2. **Improved Performance**: Reduces metadata overhead and improves query performance
3. **Storage Efficiency**: Optimal file sizes reduce storage fragmentation
4. **Seamless Integration**: Works with existing Spark SQL operations
5. **Configurable**: Adjustable target file sizes for different use cases

## Best Practices

1. **Monitor File Sizes**: Adjust `targetFileSizeMB` based on your storage system and query patterns
2. **Consider Data Volume**: For very large datasets, you might want larger target file sizes
3. **Test Performance**: Benchmark with and without the feature for your specific workloads
4. **Partition Strategy**: Combine with appropriate partitioning strategies for optimal results

## Limitations

1. **Statistics Dependency**: Requires accurate statistics for optimal partition calculation
2. **Conservative Approach**: Only repartitions when there's significant benefit (current partitions > optimal * 2)
3. **AQE Requirement**: Must have Adaptive Query Execution enabled
4. **Write Operations Only**: Only applies to output operations, not intermediate stages

## Troubleshooting

### Feature Not Working

1. Verify AQE is enabled: `spark.sql.adaptive.enabled=true`
2. Check feature is enabled: `spark.sql.adaptive.smallFileMerging.enabled=true`
3. Ensure statistics are available for the data being written

### Too Many/Few Files

1. Adjust target file size: `spark.sql.adaptive.smallFileMerging.targetFileSizeMB`
2. Check if current partition count significantly exceeds optimal count
3. Verify data size statistics are accurate

### Performance Issues

1. Monitor shuffle overhead introduced by repartitioning
2. Consider disabling for small datasets where overhead exceeds benefits
3. Adjust target file size based on your storage system's optimal file size

## Examples

### Basic Usage

```scala
// Scala API
spark.conf.set("spark.sql.adaptive.smallFileMerging.enabled", "true")
spark.conf.set("spark.sql.adaptive.smallFileMerging.targetFileSizeMB", "200")

val df = spark.range(0, 1000000, 1, 500).toDF("id")
df.write.mode("overwrite").parquet("/path/to/output")
```

```python
# Python API
spark.conf.set("spark.sql.adaptive.smallFileMerging.enabled", "true")
spark.conf.set("spark.sql.adaptive.smallFileMerging.targetFileSizeMB", "200")

df = spark.range(0, 1000000, 1, 500).toDF("id")
df.write.mode("overwrite").parquet("/path/to/output")
```

### Advanced Configuration

```sql
-- For large-scale data warehousing
SET spark.sql.adaptive.smallFileMerging.targetFileSizeMB=512;

-- For streaming applications with frequent small writes
SET spark.sql.adaptive.smallFileMerging.targetFileSizeMB=128;

-- For development/testing environments
SET spark.sql.adaptive.smallFileMerging.targetFileSizeMB=64;
```
